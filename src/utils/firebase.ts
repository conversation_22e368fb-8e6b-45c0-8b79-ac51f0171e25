import { initializeApp, getApps, getApp, FirebaseApp } from "firebase/app";
import admin from "firebase-admin";
import "dotenv/config";
import dotenv from "dotenv";

dotenv.config();


let firestore: admin.firestore.Firestore;

// Initialize Firebase Admin using environment variables
export const initializeFirebaseAdmin = (): admin.app.App => {
  if (!admin.apps.length) {
    try {
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

      if (!privateKey) {
        throw new Error("FIREBASE_PRIVATE_KEY environment variable is not set");
      }

      if (!process.env.FIREBASE_PROJECT_ID) {
        throw new Error("FIREBASE_PROJECT_ID environment variable is not set");
      }

      if (!process.env.FIREBASE_CLIENT_EMAIL) {
        throw new Error(
          "FIREBASE_CLIENT_EMAIL environment variable is not set"
        );
      }

      // Clean and format the private key
      const formattedPrivateKey = privateKey
        .replace(/\\n/g, "\n")
        .replace(/"$/, "")
        .replace(/^"/, "");

      console.log(
        "Initializing Firebase Admin with project:",
        process.env.FIREBASE_PROJECT_ID
      );

      // Use environment variables instead of JSON file
      const app = admin.initializeApp({
        credential: admin.credential.cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: formattedPrivateKey,
        }),
        databaseURL: process.env.FIREBASE_DATABASE_URL,
      });

      console.log("Firebase Admin initialized successfully");
      return app;
    } catch (error) {
      console.error("Failed to initialize Firebase Admin:", error);
      if (error instanceof Error) {
        console.error("Error details:", error.message);
        console.error("Error stack:", error.stack);
      }
      throw error;
    }
  } else {
    return admin.app();
  }
};

// Get Firestore instance
export const getFirestore = (): admin.firestore.Firestore => {
  if (!firestore) {
    const app = initializeFirebaseAdmin();
    firestore = app.firestore();
  }
  return firestore;
};

// Store OAuth2 credentials in Firebase
export const storeOAuth2Credentials = async (
  credentials: any
): Promise<void> => {
  try {
    const db = getFirestore();
    await db.collection("settings").doc("oauth2-credentials").set(credentials);
    console.log("OAuth2 credentials stored in Firebase");
  } catch (error) {
    console.error("Failed to store OAuth2 credentials in Firebase:", error);
    throw error;
  }
};

// Get OAuth2 credentials from Firebase
export const getOAuth2Credentials = async (): Promise<any> => {
  try {
    const db = getFirestore();
    const doc = await db.collection("settings").doc("oauth2-credentials").get();

    if (!doc.exists) {
      throw new Error("OAuth2 credentials not found in Firebase");
    }

    return doc.data();
  } catch (error) {
    console.error("Failed to get OAuth2 credentials from Firebase:", error);
    throw error;
  }
};

export default {
  initializeFirebaseAdmin,
  getFirestore,
  storeOAuth2Credentials,
  getOAuth2Credentials,
};
