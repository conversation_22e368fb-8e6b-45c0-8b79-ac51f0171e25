import { youtube_v3 } from "googleapis";
import { OAuth2Client } from "google-auth-library";
import { google } from "googleapis";
import * as fs from "fs";
import path from "path";
import axios from "axios";
import { getFirestore, getOAuth2Credentials } from "./firebase";
import "dotenv/config";
import { CronJob } from "cron";

// OpenAI integration for content generation
interface ContentGenerationResponse {
  title?: string;
  description: string;
  tags: string[];
}

// Updated interface to track platform-specific upload status
interface UploadStatus {
  youtube?: boolean;
  instagram?: boolean;
  facebook?: boolean;
  updatedAt?: string;
}

// Map of video names to their upload status per platform
type UploadStatusMap = Record<string, UploadStatus>;

// Add temp directory constant
const tempDir = path.join(__dirname, "../utils/temp");
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

// ✅ Save uploaded videos with platform-specific status
const saveUploadedVideos = async (
  channelId: string,
  uploadedVideos: { [key: string]: boolean }
) => {
  try {
    const db = getFirestore();
    await db.collection("uploadedVideos").doc(channelId).set({
      videos: uploadedVideos,
    });
  } catch (error) {
    console.error("Error saving uploaded videos to Firebase:", error);
  }
};

// Get YouTube Channel ID from OAuth Token
const getYouTubeChannelId = async (auth: OAuth2Client): Promise<string> => {
  const youtube = google.youtube({ version: "v3", auth });
  const response = await youtube.channels.list({
    part: ["id"],
    mine: true,
  });

  if (!response.data.items || response.data.items.length === 0) {
    throw new Error("🔴 No YouTube channel found for this account.");
  }

  return response.data.items[0].id!;
};

// ✅ Save OAuth Token to Firebase
const saveOAuthToken = async (email: string, tokens: any): Promise<void> => {
  try {
    const db = getFirestore();
    const tokenDoc = await db.collection("userTokens").doc(email).get();
    const existingTokens = tokenDoc.exists ? tokenDoc.data() : {};

    await db
      .collection("userTokens")
      .doc(email)
      .set({
        ...existingTokens,
        youtube: tokens,
        updatedAt: new Date().toISOString(),
      });
    console.log(`Token saved to Firebase for ${email}`);
  } catch (error) {
    console.error(`Error saving token to Firebase for ${email}:`, error);
    throw new Error(`Failed to save token for ${email}: ${error.message}`);
  }
};

// ✅ Load OAuth Token from Firebase
export const loadOAuthToken = async (email: string): Promise<any | null> => {
  try {
    const db = getFirestore();
    const tokenDoc = await db.collection("userTokens").doc(email).get();

    if (tokenDoc.exists) {
      const data = tokenDoc.data();
      return data || null;
    }
    return null;
  } catch (error) {
    console.error(`Error loading token from Firebase for ${email}:`, error);
    return null;
  }
};

// Authenticate user - updated to use OAuth2 credentials from Firebase
export const authenticateUser = async (user: any): Promise<OAuth2Client> => {
  try {
    // Load OAuth2 credentials from Firebase instead of file
    const credentials = await getOAuth2Credentials();
    const { client_id, client_secret, redirect_uris } = credentials.installed;

    const oauth2Client = new google.auth.OAuth2(
      client_id,
      client_secret,
      redirect_uris[0]
    );

    // Load token from Firebase
    const userToken = await loadOAuthToken(user.email);
    if (userToken) {
      console.log(`Token loaded for ${user.email}`);
      oauth2Client.setCredentials(userToken?.youtube);
    } else {
      console.log(`No token found for ${user.email}. Authentication required.`);
    }

    return oauth2Client;
  } catch (error) {
    console.error(`Error authenticating user ${user.email}:`, error);
    throw error;
  }
};

// Get new token - updated to use OAuth2 credentials from Firebase
export const getNewToken = async (
  code: string,
  email: string
): Promise<OAuth2Client> => {
  try {
    // Load OAuth2 credentials from Firebase
    const credentials = await getOAuth2Credentials();
    const { client_id, client_secret, redirect_uris } = credentials.installed;

    const oauth2Client = new OAuth2Client(
      client_id,
      client_secret,
      redirect_uris[0]
    );

    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);

    // Save token to Firebase
    await saveOAuthToken(email, tokens);

    console.log("✅ Authentication successful! Token saved to Firebase.");
    return oauth2Client;
  } catch (error) {
    console.error("🔴 Authentication failed:", error);
    throw error;
  }
};

// Load OpenAI keys from Firebase
export const loadOpenAIKeys = async (): Promise<string[]> => {
  try {
    const db = getFirestore();
    const keysDoc = await db.collection("settings").doc("openai-keys").get();

    if (keysDoc.exists) {
      return keysDoc.data()?.keys || [];
    }

    return [];
  } catch (error) {
    console.error("Error reading OpenAI keys from Firebase:", error);
    return [];
  }
};

// Platform-specific upload limits (in milliseconds)
const PLATFORM_LIMITS = {
  youtube: 24 * 60 * 60 * 1000, // 24 hours
  instagram: 24 * 60 * 60 * 1000, // 24 hours
  facebook: 24 * 60 * 60 * 1000, // 24 hours
};

// Enhanced interface for upload limit tracking
interface UploadLimitStatus {
  isLimited: boolean;
  limitStartTime: string;
  nextAllowedUploadTime: string;
  uploadCount: number;
  lastResetTime: string;
  failedAttempts: number;
  lastErrorMessage?: string;
}

// Set upload limit for a user with enhanced tracking
const setUploadLimit = async (email: string, mediaType: string) => {
  const db = getFirestore();
  const now = new Date();
  const nextAllowedTime = new Date(now.getTime() + PLATFORM_LIMITS[mediaType]);

  const limitRef = db
    .collection("uploadLimits")
    .doc(email)
    .collection(mediaType)
    .doc("status");

  // Get existing data if any
  const existingDoc = await limitRef.get();
  const existingData = existingDoc.exists
    ? (existingDoc.data() as UploadLimitStatus)
    : null;

  await limitRef.set({
    isLimited: true,
    limitStartTime: now.toISOString(),
    nextAllowedUploadTime: nextAllowedTime.toISOString(),
    uploadCount: (existingData?.uploadCount || 0) + 1,
    lastResetTime: existingData?.lastResetTime || now.toISOString(),
    failedAttempts: (existingData?.failedAttempts || 0) + 1,
    lastErrorMessage: `Upload limit reached at ${now.toISOString()}`,
  });
};

// Enhanced check for upload limits
const checkUploadLimitStatus = async (
  email: string,
  mediaType: string
): Promise<UploadLimitStatus | null> => {
  const db = getFirestore();
  const limitDoc = await db
    .collection("uploadLimits")
    .doc(email)
    .collection(mediaType)
    .doc("status")
    .get();

  if (!limitDoc.exists) {
    return null;
  }

  const limitData = limitDoc.data() as UploadLimitStatus;
  const now = new Date();
  const nextAllowedTime = new Date(limitData.nextAllowedUploadTime);
  const lastResetTime = new Date(limitData.lastResetTime);
  const daysSinceReset =
    (now.getTime() - lastResetTime.getTime()) / (24 * 60 * 60 * 1000);

  // Reset counters if it's been more than 24 hours since last reset
  if (daysSinceReset >= 1) {
    await db
      .collection("uploadLimits")
      .doc(email)
      .collection(mediaType)
      .doc("status")
      .set({
        isLimited: false,
        limitStartTime: now.toISOString(),
        nextAllowedUploadTime: now.toISOString(),
        uploadCount: 0,
        lastResetTime: now.toISOString(),
        failedAttempts: 0,
      });
    return null;
  }

  if (limitData.isLimited && now < nextAllowedTime) {
    return limitData;
  }

  // If cooldown period has passed, remove the limit
  if (limitData.isLimited && now >= nextAllowedTime) {
    await db
      .collection("uploadLimits")
      .doc(email)
      .collection(mediaType)
      .doc("status")
      .delete();
    return null;
  }

  return null;
};

// Helper function to mask API key in logs
function maskAPIKey(key: string): string {
  if (key.length <= 8) return key;
  return key.substring(0, 4) + "..." + key.substring(key.length - 4);
}

// Function to store video upload information in Firestore
const storeVideoUploadInfo = async (
  email: string,
  videoName: string,
  platform: string,
  platformVideoId: string,
  title: string,
  description: string,
  tags: string[],
  url: string
) => {
  const db = getFirestore();
  const videoRef = db
    .collection("userVideos")
    .doc(email)
    .collection("videos")
    .doc(videoName);

  const videoData = {
    title,
    description,
    tags,
    uploads: {
      [platform]: {
        videoId: platformVideoId,
        url: url,
        uploadedAt: new Date().toISOString(),
      },
    },
    lastUpdated: new Date().toISOString(),
  };

  await videoRef.set(videoData, { merge: true });
};

// Generate tags for videos
async function generateTags(
  description: string,
  videoType: string
): Promise<string[]> {
  console.log(`📝 Using description: ${description.substring(0, 100)}...`);

  try {
    const db = getFirestore();
    const keysDoc = await db.collection("settings").doc("openai-keys").get();
    const apiKeys = keysDoc.data()?.keys || [];

    if (apiKeys.length === 0) {
      console.log("⚠️ No API keys found, using default tags");
      return [videoType, "video", "content"];
    }

    const apiKey = apiKeys[Math.floor(Math.random() * apiKeys.length)];
    console.log("🔑 Using API key:", maskAPIKey(apiKey));

    const prompt = `Generate 10-15 relevant tags for a ${videoType} or with this description: "${description}". 
    The tags should:
    1. Be relevant to the video content
    2. Include a mix of broad and specific terms
    3. Include trending hashtags if applicable
    4. Be optimized for search
    Return only an array of tags in JSON format.`;

    console.log("🤖 Sending request to Gemini API...");
    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 800,
        },
      },
      {
        headers: { "Content-Type": "application/json" },
      }
    );

    let content = response.data.candidates[0].content.parts[0].text;
    content = content
      .replace(/```json\n/i, "")
      .replace(/```/i, "")
      .replace(/\/\/.*$/gm, "") // Remove any line comments
      .replace(/\/\*[\s\S]*?\*\//g, "") // Remove any block comments
      .trim();

    try {
      // Try to find the JSON array in the content
      const jsonMatch = content.match(/\[.*\]/s);
      if (jsonMatch) {
        const tags = JSON.parse(jsonMatch[0]);
        if (!Array.isArray(tags)) {
          return [videoType, "video", "content"];
        }
        return tags;
      }
      return [videoType, "video", "content"];
    } catch (parseError) {
      console.error("🔴 Error parsing tags:", parseError);
      return [videoType, "video", "content"];
    }
  } catch (error) {
    console.error("🔴 Error generating tags:", error);
    return [videoType, "video", "content"];
  }
}

// Update uploadToYouTube function to handle upload limits
const uploadToYouTube = async (
  auth: OAuth2Client,
  filePath: string,
  title: string,
  description: string,
  tags: string[],
  uploadedVideos: { [key: string]: boolean },
  channelId: string,
  generatedTitle?: string,
  userEmail?: string
) => {
  const videoTitle = generatedTitle || title;

  if (uploadedVideos[title]) {
    console.log(`⚠️ Skipping already uploaded video to YouTube: ${title}`);
    return;
  }

  try {
    const youtube: youtube_v3.Youtube = google.youtube({
      version: "v3",
      auth,
    });

    console.log(`📤 Uploading video to YouTube: ${videoTitle}...`);

    try {
      const response = await youtube.videos.insert({
        part: ["snippet", "status"],
        requestBody: {
          snippet: {
            title: videoTitle,
            description,
            tags,
            categoryId: "22",
            defaultAudioLanguage: "en",
            defaultLanguage: "en",
          },
          status: {
            privacyStatus: "public",
          },
        },
        media: {
          body: fs.createReadStream(filePath),
        },
      });

      console.log(
        `✅ Video uploaded successfully to YouTube! Video ID: ${response.data.id}`
      );

      // Mark as uploaded
      uploadedVideos[title] = true;
      saveUploadedVideos(channelId, uploadedVideos);
    } catch (error: any) {
      // Check for upload limit error
      if (
        error.message.includes(
          "The user has exceeded the number of videos they may upload"
        ) &&
        userEmail
      ) {
        await setUploadLimit(userEmail, "youtube");
        throw new Error(
          "Upload limit reached. Uploads will resume in 24 hours."
        );
      }
      throw error;
    }
  } catch (error) {
    console.error("🔴 Error uploading video to YouTube:", error);
    throw error;
  }
};

// Rate limiting configuration
const RATE_LIMITS = {
  youtube: { maxUploads: 10, windowHours: 24, minDelaySeconds: 5 },
  instagram: { maxUploads: 25, windowHours: 24, minDelaySeconds: 5 },
  facebook: { maxUploads: 25, windowHours: 24, minDelaySeconds: 5 },
};

// Add delay function
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// Check rate limits before upload
async function checkRateLimit(
  email: string,
  mediaType: string
): Promise<boolean> {
  const db = getFirestore();
  const now = new Date();
  const windowStart = new Date(
    now.getTime() - RATE_LIMITS[mediaType].windowHours * 60 * 60 * 1000
  );

  try {
    // First try a simpler query that doesn't require composite index
    const uploadsRef = db.collection("uploadHistory");
    const simpleQuery = await uploadsRef
      .where("email", "==", email)
      .where("mediaType", "==", mediaType)
      .get();

    const recentUploads = simpleQuery.docs.filter((doc) => {
      const timestamp = new Date(doc.data().timestamp);
      return timestamp >= windowStart;
    });

    const uploadCount = recentUploads.length;

    if (uploadCount >= RATE_LIMITS[mediaType].maxUploads) {
      // Set upload limit if rate limit exceeded
      await setUploadLimit(email, mediaType);
      return true;
    }

    // Log the upload attempt
    await db.collection("uploadHistory").add({
      email,
      mediaType,
      timestamp: now.toISOString(),
      success: true,
    });

    return false;
  } catch (error) {
    console.error("Error checking rate limits--->>>:", {
      error: error.message,
      email,
      mediaType,
      timestamp: now.toISOString(),
    });

    // but log the attempt
    await db.collection("uploadHistory").add({
      email,
      mediaType,
      timestamp: now.toISOString(),
      success: true,
      errorChecking: true,
    });

    return false;
  }
}

// Update uploadToSocialPlatforms function to handle the new return types
export async function uploadToSocialPlatforms(
  auth: any,
  user: any,
  folderData: any,
  allTokens: any
) {
  const { email } = user;
  console.log(`🚀 Starting upload process for user: ${email}`);

  try {
    // Get user tokens from Firebase
    // console.log("🔑 Fetching user tokens from Firebase...");
    const db = getFirestore();
    // const tokenDoc = await db.collection("userTokens").doc(email).get();
    // const allTokens = tokenDoc.data();

    // Check rate limits for each platform before proceeding
    try {
      const platformRateLimits = await Promise.allSettled([
        user.platformStatus.youtube && checkRateLimit(email, "youtube"),
        user.platformStatus.facebook && checkRateLimit(email, "facebook"),
        user.platformStatus.instagram && checkRateLimit(email, "instagram"),
      ]);

      // Filter out platforms that are rate limited
      const rateLimitedPlatforms = platformRateLimits
        .map((result, index) => {
          if (result.status === "fulfilled" && result.value === true) {
            return ["youtube", "facebook", "instagram"][index];
          }
          return null;
        })
        .filter(Boolean);

      if (rateLimitedPlatforms.length > 0) {
        console.warn(
          `⚠️ Rate limits reached for platforms: ${rateLimitedPlatforms.join(
            ", "
          )}`
        );
        // Temporarily disable rate-limited platforms
        rateLimitedPlatforms.forEach((platform) => {
          if (user.platformStatus[platform]) {
            console.log(
              `Temporarily disabling ${platform} uploads due to rate limit`
            );
            user.platformStatus[platform] = false;
          }
        });
      }

      // Check if we still have any active platforms
      const hasActivePlatforms = Object.values(user.platformStatus).some(
        (status) => status
      );
      if (!hasActivePlatforms) {
        throw new Error(
          "All platforms are currently rate limited. Please try again later."
        );
      }
    } catch (rateLimitError) {
      console.error("Error checking rate limits:==>>", rateLimitError);
      // Continue with upload attempt even if rate limit check fails
      console.log("Proceeding with upload despite rate limit check failure");
      return false;
    }

    // Get videos from Google Drive
    console.log("📂 Fetching videos from Google Drive...");
    const drive = google.drive({ version: "v3", auth });
    const files = await drive.files.list({
      q: `'${folderData.folderId}' in parents and mimeType contains 'video/'`,
      fields: "files(id, name, mimeType, webContentLink)",
      spaces: "drive",
    });

    if (!files.data.files || files.data.files.length === 0) {
      console.log("⚠️ No videos found in the specified folder");
      return;
    }

    // Initialize uploadedVideos as an object instead of a Set
    const uploadedVideos: { [key: string]: boolean } = {};
    const userVideosRef = db
      .collection("userVideos")
      .doc(email)
      .collection("videos");
    const uploadedVideosSnapshot = await userVideosRef.get();
    uploadedVideosSnapshot.docs.forEach((doc) => {
      uploadedVideos[doc.data().title] = true;
    });

    // Process each video
    for (const file of files.data.files) {
      if (uploadedVideos[file.name]) {
        console.log(`⚠️ Skipping already uploaded video: ${file.name}`);
        continue;
      }
      let tempFilePath;
      try {
        // Add delay between video processing
        await delay(5000); // 5 second delay between videos

        // Download video file
        console.log("⬇️ Downloading video file...");
        try {
          const driveResponse: any = await drive.files.get(
            {
              fileId: file.id,
              alt: "media",
            },
            { responseType: "stream" }
          );

          tempFilePath = path.join(tempDir, `${file.id}.mp4`);
          console.log(`📝 Saving to temporary file: ${tempFilePath}`);

          // Ensure temp directory exists
          if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
          }

          const writeStream = fs.createWriteStream(tempFilePath);
          await new Promise((resolve, reject) => {
            driveResponse.data
              .on("end", resolve)
              .on("error", (err) => {
                console.error("Error downloading file from Drive:", err);
                reject(err);
              })
              .pipe(writeStream);

            writeStream.on("error", (err) => {
              console.error("Error writing file:", err);
              reject(err);
            });
          });

          // Verify file exists and has size before proceeding
          if (!fs.existsSync(tempFilePath)) {
            throw new Error(`Failed to create temporary file: ${tempFilePath}`);
          }

          const stats = fs.statSync(tempFilePath);
          if (stats.size === 0) {
            throw new Error(`Downloaded file is empty: ${tempFilePath}`);
          }

          console.log(
            `✅ File downloaded successfully: ${tempFilePath} (${stats.size} bytes)`
          );
        } catch (downloadError) {
          console.error(
            `❌ Error downloading video ${file.name}:`,
            downloadError
          );
          continue; // Skip to next video if download fails
        }

        // Generate description and tags
        console.log("🤖 Generating description and tags...");
        const description = await generateDescription(
          file.name,
          folderData.videoType
        );
        const tags = await generateTags(description, folderData.videoType);

        if (allTokens.youtube && user.platformStatus.youtube) {
          console.log(
            " Upload to youtube if authenticated",
            user.platformStatus.youtube
          );
          console.log("▶️ Uploading to YouTube...");
          try {
            const isLimitReached = await isLimited(email, "youtube");
            console.log("isLimitReached", isLimitReached);
            // Get YouTube channel ID
            const youtube = google.youtube({ version: "v3", auth });
            const channelResponse = await youtube.channels.list({
              part: ["id"],
              mine: true,
            });

            if (
              !channelResponse.data.items ||
              channelResponse.data.items.length === 0
            ) {
              throw new Error("No YouTube channel found");
            }

            const channelId = channelResponse.data.items[0].id;

            // Verify file exists before attempting upload
            if (!fs.existsSync(tempFilePath)) {
              throw new Error(`Video file not found: ${tempFilePath}`);
            }

            try {
              await uploadToYouTube(
                auth,
                tempFilePath,
                file.name,
                description,
                tags,
                uploadedVideos,
                channelId,
                file.name,
                email
              );

              // Store video info in database
              await storeVideoUploadInfo(
                email,
                file.name,
                "youtube",
                file.id,
                file.name,
                description,
                tags,
                `https://youtube.com/watch?v=${file.id}`
              );
            } catch (uploadError) {
              console.error("❌ Error during YouTube upload:", uploadError);
              if (uploadError.message.includes("Upload limit reached")) {
                throw uploadError; // Re-throw rate limit errors
              }
              // For other errors, continue with next video
              continue;
            }
          } catch (error: any) {
            console.error("❌ Error in YouTube upload process:", error.message);
            if (error.message.includes("Upload limit reached")) {
              throw error; // Re-throw rate limit errors
            }
            // For other errors, continue with next video
            continue;
          }
        }

        if (allTokens.facebook && user.platformStatus.facebook) {
          console.log(
            " Upload to Facebook if authenticated",
            user.platformStatus.facebook
          );
          console.log("▶️ Uploading to Facebook...", allTokens.facebook);
          try {
            const isLimitReached = await isLimited(email, "facebook");
            console.log("isLimitReached", isLimitReached);
            const pages = allTokens.facebook.pages;
            console.log("pages", pages);
            for (const page of pages) {
              console.log(`📤 Uploading to Facebook page: ${page}`);
              const fbResponse = await fetch(
                `https://graph.facebook.com/v18.0/${page}/videos`,
                {
                  method: "POST",
                  headers: {
                    Authorization: `Bearer ${allTokens.facebook.accessToken}`,
                  },
                  body: JSON.stringify({
                    file_url: file.webContentLink,
                    title: file.name,
                    description,
                    tags: tags.join(","),
                    privacy: { value: "SELF" },
                  }),
                }
              );
              const fbData = await fbResponse.json();
              if (fbData.error) {
                throw new Error(fbData.error.message);
              }

              const fbVideoId = fbData.id;
              const fbVideoUrl = `https://www.facebook.com/watch?v=${fbVideoId}`;

              console.log("✅ Successfully uploaded to Facebook");

              const saveInDb = await storeVideoUploadInfo(
                email,
                file.name, // Use file.name as the doc ID
                "facebook",
                fbVideoId,
                file.name,
                description,
                tags,
                fbVideoUrl
              );
              console.log("facebook saveInDb", saveInDb);
            }
          } catch (error) {
            console.error("❌ Error uploading to Facebook:", error.message);

            // Check for upload limit error
            if (error.message.includes(" exceeded")) {
              await setUploadLimit(email, "facebook");
              throw new Error(
                "Upload limit reached. Uploads will resume in 24 hours."
              );
            }
            throw error;
          }
        }

        if (allTokens.instagram && user.platformStatus.instagram) {
          console.log(
            " Upload to Instagram if authenticated",
            user.platformStatus.instagram
          );
          console.log("▶️ Uploading to Instagram...");
          try {
            const isLimitReached = await isLimited(email, "instagram");
            console.log("isLimitReached", isLimitReached);
            const igResponse = await fetch(
              `https://graph.facebook.com/v18.0/${allTokens.instagram.userId}/media`,
              {
                method: "POST",
                headers: {
                  Authorization: `Bearer ${allTokens.instagram.accessToken}`,
                },
                body: JSON.stringify({
                  media_type: "REELS",
                  video_url: file.webContentLink,
                  caption: `${file.name}\n\n${description}\n\n#${tags.join(
                    " #"
                  )}`,
                  share_to_feed: true,
                }),
              }
            );
            console.log("igResponse", igResponse);
            const igData = await igResponse.json();
            if (igData.error) {
              throw new Error(igData.error.message);
            }

            // Publish the media
            console.log("📤 Publishing to Instagram...");
            const publishResponse = await fetch(
              `https://graph.facebook.com/v18.0/${allTokens.instagram.userId}/media_publish`,
              {
                method: "POST",
                headers: {
                  Authorization: `Bearer ${allTokens.instagram.accessToken}`,
                },
                body: JSON.stringify({
                  creation_id: igData.id,
                }),
              }
            );
            console.log("publishResponse", publishResponse);
            const publishData = await publishResponse.json();
            console.log("publishData", publishData);
            const igVideoId = igData.id;
            const igVideoUrl = `https://www.instagram.com/p/${publishData.id}`;

            const saveInDb = await storeVideoUploadInfo(
              email,
              file.name, // Use file.name as the doc ID
              "instagram",
              igVideoId,
              file.name,
              description,
              tags,
              igVideoUrl
            );

            console.log("✅ Successfully uploaded to Instagram");
            console.log("Instagram saveInDb", saveInDb);
          } catch (error) {
            console.error("❌ Error uploading to Instagram:", error.message);
            // Check for upload limit error
            if (error.message.includes(" exceeded")) {
              await setUploadLimit(email, "instagram");
              throw new Error(
                "Upload limit reached. Uploads will resume in 24 hours."
              );
            }
            throw error;
          }
        }

        // Clean up temp file
        console.log("🧹 Cleaning up temporary files...");
        try {
          if (fs.existsSync(tempFilePath)) {
            fs.unlinkSync(tempFilePath);
            console.log("✅ Temporary file deleted successfully");
          } else {
            console.log("⚠️ Temporary file not found for deletion");
          }
        } catch (error) {
          console.error("Error deleting temporary file:", error);
        }
        console.log("✅ Cleanup complete");
      } catch (error) {
        console.error(`❌ Error processing video ${file.name}:`, error);
        if (error.message.includes("Upload limit reached")) {
          // If we hit the upload limit, stop processing more videos
          throw error;
          // return error;
        }
      }
    }
    console.log("\n✨ All videos processed successfully");
  } catch (error) {
    console.error("Error in uploadToSocialPlatforms:", error);
    throw error;
  }
}

export async function scheduleUploadsForUser(user: any) {
  try {
    // Get user tokens from Firebase
    const db = getFirestore();
    const tokenDoc = await db.collection("userTokens").doc(user.email).get();
    const tokens = tokenDoc.data();
    if (!tokens) {
      console.log(
        `No tokens found for user ${user.email}, skipping scheduling`
      );
      return;
    }

    console.log("Check if user has any active platforms", user.platformStatus);
    const hasActivePlatform = Object.entries(user.platformStatus).some(
      ([platform, isActive]) => isActive && tokens[platform]
    );
    console.log("hasActivePlatform", hasActivePlatform);
    if (!hasActivePlatform) {
      console.log(
        `No active platforms found for user ${user.email}, skipping scheduling`
      );
      return;
    }

    // Schedule uploads for each folder
    for (const folder of user.googleDriveLinks) {
      const cronJob = new CronJob(
        folder.cronSchedule,
        // "*/30 * * * * *",
        async () => {
          try {
            const auth = await authenticateUser(user);
            await uploadToSocialPlatforms(auth, user, folder, tokens);
          } catch (error) {
            console.error(
              `Error in scheduled upload for ${user.email}:`,
              error
            );
          }
        },
        null,
        true,
        "UTC"
      );

      cronJob.start();
      console.log(
        `Scheduled uploads for ${user.email} with schedule ${folder.cronSchedule}`
      );
    }
  } catch (error) {
    console.error(`Error scheduling uploads for ${user.email}:`, error);
    throw error;
  }
}

// Add content generation functions
async function generateDescription(
  videoName: string,
  videoType: string
): Promise<string> {
  console.log(`\n🤖 Generating description for video: ${videoName}`);

  try {
    const db = getFirestore();
    console.log("🔑 Fetching API keys from Firestore...");
    const keysDoc = await db.collection("settings").doc("openai-keys").get();
    const apiKeys = keysDoc.data()?.keys || [];

    if (apiKeys.length === 0) {
      console.log("⚠️ No API keys found, using default description");
      return `Check out this ${videoType} video: ${videoName}`;
    }

    const apiKey = apiKeys[Math.floor(Math.random() * apiKeys.length)];
    console.log("🔑 Using API key:", maskAPIKey(apiKey));

    const prompt = `Generate an engaging social media description for a ${videoType} video titled "${videoName}". 
    The description should:
    1. Be attention-grabbing and engaging
    2. Include relevant emojis
    3. Include 3-5 relevant hashtags
    4. Be optimized for social media sharing
    5. Include a call to action
    6. Be between 150-300 characters
    7. Maintain a professional yet friendly tone
    8. Include relevant keywords for SEO`;

    console.log("📤 Sending request to Gemini API...");
    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        contents: [{ parts: [{ text: prompt }] }],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 800,
        },
      },
      {
        headers: { "Content-Type": "application/json" },
      }
    );

    console.log("✅ Received response from Gemini API");
    let content = response.data.candidates[0].content.parts[0].text;

    // Clean up the response text
    content = content
      .replace(/```json\n/i, "")
      .replace(/```/i, "")
      .trim();
    // console.log("🧹 Cleaned response:", content);

    try {
      // Try to parse as JSON first
      console.log("🔍 Attempting to parse JSON...");
      const parsedContent = JSON.parse(content);
      console.log("✅ Successfully parsed JSON");
      return (
        parsedContent.description ||
        `Check out this ${videoType} video: ${videoName}`
      );
    } catch (error) {
      console.log("🔍 Parsing failed, using default description");
      return `Check out this ${videoType} video: ${videoName}`;
    }
  } catch (error) {
    console.error("🔴 Error generating description:", error);
    return `Check out this ${videoType} video: ${videoName}`;
  }
}

async function isLimited(email: string, mediaType: string): Promise<boolean> {
  try {
    // Check for platform upload limits
    const limitStatus = await checkUploadLimitStatus(email, mediaType);
    if (limitStatus) {
      const nextAllowedTime = new Date(limitStatus.nextAllowedUploadTime);
      const timeRemaining = nextAllowedTime.getTime() - new Date().getTime();
      const hoursRemaining = Math.ceil(timeRemaining / (1000 * 60 * 60));

      // Log detailed limit information
      console.log({
        message: `⚠️ Upload limit reached for ${email} on ${mediaType}`,
        uploadCount: limitStatus.uploadCount,
        failedAttempts: limitStatus.failedAttempts,
        hoursRemaining,
        lastError: limitStatus.lastErrorMessage,
      });

      // Store the limit event in a separate collection for monitoring
      const db = getFirestore();
      await db.collection("uploadLimitEvents").add({
        email,
        mediaType,
        timestamp: new Date().toISOString(),
        limitStatus,
        hoursRemaining,
      });

      throw new Error(
        `Upload limit reached for ${mediaType}. Uploads will resume in ${hoursRemaining} hours. ` +
          `Failed attempts: ${limitStatus.failedAttempts}`
      );
    }
    return false;
  } catch (error) {
    // Log the error but don't expose internal details
    console.error("Error checking upload limits:", {
      email,
      mediaType,
      error: error.message,
      timestamp: new Date().toISOString(),
    });

    // Re-throw with a user-friendly message
    if (error.message.includes("Upload limit reached")) {
      throw error;
    }
    throw new Error("Unable to check upload limits. Please try again later.");
  }
}
