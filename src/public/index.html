<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Social Media Uploader - Google Drive to Multiple Platforms</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f9f9f9;
      }
      .container {
        max-width: 800px;
        margin: auto;
      }
      .header {
        text-align: center;
        margin-bottom: 30px;
      }
      .header h1 {
        color: #c4302b; /* YouTube red */
      }
      .platform-icons {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-bottom: 20px;
      }
      .platform-icon {
        font-size: 36px;
        color: #333;
      }
      .platform-icon.youtube {
        color: #c4302b;
      }
      .platform-icon.instagram {
        color: #e1306c;
      }
      .platform-icon.facebook {
        color: #4267b2;
      }
      form {
        background: white;
        padding: 25px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      .form-section {
        border-top: 1px solid #eee;
        padding-top: 15px;
        margin-top: 15px;
      }
      .form-section-title {
        margin-bottom: 15px;
        color: #333;
        font-weight: bold;
      }
      .form-row {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 15px;
      }
      .form-group {
        flex: 1;
        min-width: 250px;
      }
      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: #555;
      }
      input[type="text"],
      input[type="email"],
      input[type="password"],
      textarea,
      select {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-sizing: border-box;
        font-size: 14px;
      }
      textarea {
        resize: vertical;
        min-height: 80px;
      }
      .checkbox-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
      }
      .checkbox-group label {
        margin-left: 10px;
        margin-bottom: 0;
      }
      button {
        background: #4caf50;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        transition: background-color 0.3s;
      }
      button:hover {
        background: #45a049;
      }
      button.secondary {
        background: #2196f3;
      }
      button.secondary:hover {
        background: #0b7dda;
      }
      .button-row {
        display: flex;
        gap: 15px;
        margin-top: 20px;
      }
      #keysList {
        list-style-type: none;
        padding: 0;
        margin-bottom: 15px;
      }
      #keysList li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px;
        border: 1px solid #ddd;
        margin-bottom: 5px;
        border-radius: 4px;
      }
      .remove-key {
        background: #f44336;
        color: white;
        border: none;
        padding: 3px 8px;
        border-radius: 3px;
        cursor: pointer;
      }
      .toggle-container {
        display: inline-block;
        position: relative;
        width: 60px;
        height: 30px;
      }
      .toggle-input {
        opacity: 0;
        width: 0;
        height: 0;
      }
      .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: 0.4s;
        border-radius: 30px;
      }
      .toggle-slider:before {
        position: absolute;
        content: "";
        height: 22px;
        width: 22px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: 0.4s;
        border-radius: 50%;
      }
      .toggle-input:checked + .toggle-slider {
        background-color: #2196f3;
      }
      .toggle-input:checked + .toggle-slider:before {
        transform: translateX(30px);
      }
      .platform-credentials {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        margin-top: 10px;
        background-color: #f9f9f9;
      }
      .alert {
        padding: 15px;
        margin-bottom: 20px;
        border-radius: 4px;
      }
      .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .alert-error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
      }
      .videos-list {
        margin-top: 15px;
      }

      .video-card {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 15px;
        margin-bottom: 20px;
      }

      .video-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .video-url {
        color: #c4302b;
        margin-bottom: 8px;
        word-break: break-all;
      }

      .video-description {
        margin-bottom: 8px;
        color: #555;
      }

      .video-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: 10px;
      }

      .video-tag {
        background-color: #f1f1f1;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        color: #333;
      }

      .video-date {
        color: #777;
        font-size: 12px;
        margin-top: 8px;
      }

      .user-list {
        margin-top: 15px;
        overflow-x: auto;
      }

      .status-active {
        background-color: #4caf50;
      }

      .status-inactive {
        background-color: #f44336;
      }

      .toggle-status-btn:hover {
        opacity: 0.9;
      }

      .auth-button {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 20px;
        border-radius: 4px;
        font-weight: bold;
        cursor: pointer;
        transition: background-color 0.3s;
        border: none;
        color: white;
      }

      .auth-button.youtube {
        background-color: #c4302b;
      }

      .auth-button.facebook {
        background-color: #4267b2;
      }

      .auth-button.instagram {
        background-color: #e1306c;
      }

      .auth-button:hover {
        opacity: 0.9;
      }

      .auth-status {
        margin-top: 10px;
        padding: 10px;
        border-radius: 4px;
      }

      .auth-status.connected {
        background-color: #d4edda;
        color: #155724;
      }

      .auth-status.error {
        background-color: #f8d7da;
        color: #721c24;
      }

      .platform-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        background-color: #fff;
      }

      .platform-credentials {
        margin-top: 15px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
      }
    </style>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
    />
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Social Media Uploader</h1>
        <p>
          Upload videos from Google Drive to YouTube, Instagram, and Facebook
          automatically
        </p>

        <div class="platform-icons">
          <i class="fab fa-youtube platform-icon youtube"></i>
          <i class="fab fa-instagram platform-icon instagram"></i>
          <i class="fab fa-facebook platform-icon facebook"></i>
        </div>
      </div>

      <div id="alert-container"></div>

      <!-- OpenAI API Keys Management -->
      <form id="apiKeysForm">
        <h2>OpenAI API Keys Management</h2>
        <p>
          Add your OpenAI API keys to generate optimized descriptions and tags
          for your content
        </p>

        <div class="form-row">
          <div class="form-group">
            <label for="apiKey">OpenAI API Key:</label>
            <input type="text" id="apiKey" placeholder="AIza-..." />
          </div>
          <div class="form-group" style="flex: 0 0 auto; align-self: flex-end">
            <button type="button" id="addKeyBtn">Add Key</button>
          </div>
        </div>

        <div id="keysList"></div>

        <div class="button-row">
          <button type="submit" disabled>Save API Keys</button>
        </div>
      </form>

      <!-- User Configuration Form -->
      <form id="userConfigForm">
        <h2>Social Media Upload Configuration</h2>

        <!-- Add saved configurations dropdown -->
        <div class="form-section">
          <div class="form-section-title">Saved Configurations</div>
          <div class="form-row">
            <div class="form-group">
              <label for="savedConfigs">Select Saved Configuration:</label>
              <select id="savedConfigs">
                <option value="">-- Select a saved configuration --</option>
              </select>
            </div>
            <div class="form-group">
              <label for="configName">Configuration Name:</label>
              <input
                type="text"
                id="configName"
                placeholder="Enter a name for this configuration"
              />
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="form-section-title">Google Account Details</div>
          <div class="form-row">
            <div class="form-group">
              <label for="email">Google Account Email:</label>
              <input
                type="email"
                id="email"
                required
                placeholder="<EMAIL>"
              />
            </div>
          </div>
        </div>

        <div class="form-section">
          <div class="form-section-title">Google Drive Configuration</div>
          <div class="form-row">
            <div class="form-group">
              <label for="folderId">Google Drive Folder ID:</label>
              <input
                type="text"
                id="folderId"
                required
                placeholder="1a2b3c4d5e6f7g8h9i0j"
              />
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="folderDescription">Folder Content Description:</label>
              <textarea
                id="folderDescription"
                placeholder="Describe the content in this folder (e.g., 'Fitness workout tutorials for beginners')"
              ></textarea>
              <small
                >This description helps generate better tags and metadata with
                OpenAI</small
              >
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="videoType">Video Category:</label>
              <input
                type="text"
                id="videoType"
                required
                placeholder="Enter video category (e.g., Educational, Gaming, Vlog)"
              />
            </div>

            <div class="form-group">
              <label for="cronSchedule">Upload Schedule:</label>
              <select id="cronSchedule" required>
                <option value="*/10 * * * * *">Every 10 seconds</option>
                <option value="*/30 * * * * *">Every 30 seconds</option>
                <option value="* * * * *" selected>Every minute</option>
                <option value="*/5 * * * *">Every 5 minutes</option>
                <option value="*/15 * * * *">Every 15 minutes</option>
                <option value="*/30 * * * *">Every 30 minutes</option>
                <option value="0 * * * *">Every hour</option>
                <option value="0 */2 * * *">Every 2 hours</option>
                <option value="0 */4 * * *">Every 4 hours</option>
                <option value="0 */6 * * *">Every 6 hours</option>
                <option value="0 */12 * * *">Every 12 hours</option>
                <option value="0 0 * * *">Daily (midnight)</option>
                <option value="0 12 * * *">Daily (noon)</option>
                <option value="0 0 * * 1">Weekly (Monday)</option>
                <option value="0 0 1 * *">Monthly (1st day)</option>
                <option value="0 0 15 * *">Monthly (15th day)</option>
              </select>
            </div>
          </div>
        </div>

        <div class="button-row">
          <button type="submit">Save Configuration</button>
          <button type="button" id="saveAsNewBtn">
            Save as New Configuration
          </button>
          <button type="button" id="deleteConfigBtn">
            Delete Configuration
          </button>
        </div>
      </form>

      <!-- Add this before the closing </div> of the container -->
      <div class="form-section">
        <div class="form-section-title">User Management</div>
        <div id="userManagementContainer" style="margin-top: 20px">
          <h3>User List</h3>
          <div class="user-list">
            <table style="width: 100%; border-collapse: collapse">
              <thead>
                <tr style="background-color: #f2f2f2; text-align: left">
                  <th style="padding: 12px; border-bottom: 1px solid #ddd">
                    Email
                  </th>
                  <th style="padding: 12px; border-bottom: 1px solid #ddd">
                    Status
                  </th>
                  <th style="padding: 12px; border-bottom: 1px solid #ddd">
                    Actions
                  </th>
                  <th style="padding: 12px; border-bottom: 1px solid #ddd">
                    Folders
                  </th>
                  <th style="padding: 12px; border-bottom: 1px solid #ddd">
                    Videos
                  </th>
                </tr>
              </thead>
              <tbody id="userTableBody">
                <!-- User rows will be added here dynamically -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- Videos List Container -->
      <div id="userVideosContainer" style="display: none; margin-top: 20px">
        <h3>User's Uploaded Videos</h3>
        <div id="videosList" class="videos-list"></div>
      </div>
    </div>

    <script>
      document.addEventListener("DOMContentLoaded", function () {
        // Initialize OpenAI API Keys
        let apiKeys = [];

        // Make these functions available in global scope
        window.loadAllUsers = function () {
          console.log("loadAllUsers");
          fetch("/users")
            .then((response) => response.json())
            .then((data) => {
              console.log(data, "===========>>>>>>>>");
              const userTableBody = document.getElementById("userTableBody");
              userTableBody.innerHTML = "";
              document.getElementById("userManagementContainer").style.display =
                "block";

              data.users.forEach((user) => {
                const row = document.createElement("tr");
                const isActive = user.isActive !== false;
                const statusClass = isActive
                  ? "status-active"
                  : "status-inactive";
                const statusText = isActive ? "Active" : "Inactive";
                const folderCount = user.googleDriveLinks?.length || 0;

                const actionButtons = `
                  <div style="display: flex; gap: 8px; align-items: center;">
                    <button class="toggle-status-btn ${statusClass}" 
                            onclick="toggleUserStatus('${
                              user.email
                            }', ${!isActive})"
                            style="padding: 4px 8px; border-radius: 4px; border: none; color: white; cursor: pointer;">
                      ${isActive ? "Deactivate" : "Activate"}
                    </button>
                    ${
                      isActive
                        ? `
                      <button class="auth-button youtube" onclick="handleYouTubeAuth('${user.email}')" style="padding: 4px 8px;">
                        <i class="fab fa-youtube"></i> YouTube
                      </button>
                      <button class="auth-button facebook" onclick="handleFacebookAuth('${user.email}')" style="padding: 4px 8px;">
                        <i class="fab fa-facebook"></i> Facebook
                      </button>
                      <button class="auth-button instagram" onclick="handleInstagramAuth('${user.email}')" style="padding: 4px 8px;">
                        <i class="fab fa-instagram"></i> Instagram
                      </button>
                    `
                        : ""
                    }
                  </div>
                `;

                row.innerHTML = `
                  <td style="padding: 12px;">${user.email}</td>
                  <td style="padding: 12px;">
                    <span class="${statusClass}" style="padding: 4px 8px; border-radius: 4px; font-size: 12px; color: white;">
                      ${statusText}
                    </span>
                  </td>
                  <td style="padding: 12px;">${actionButtons}</td>
                  <td style="padding: 12px;">${folderCount} folder${
                  folderCount !== 1 ? "s" : ""
                }</td>
                  <td style="padding: 12px;">
                    <button onclick="viewUserVideos('${
                      user.email
                    }')" class="secondary" style="padding: 4px 8px;">
                      View Videos
                    </button>
                  </td>
                `;

                userTableBody.appendChild(row);
              });

              showAlert(`Loaded ${data.users.length} users`, "success");
            })
            .catch((error) => {
              console.error("Error loading users:", error);
              showAlert("Failed to load users", "error");
            });
        };

        window.toggleUserStatus = async function (email, newStatus) {
          try {
            const response = await fetch("/update-user-status", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ email, isActive: newStatus }),
            });

            const data = await response.json();
            if (data.error) {
              showAlert(data.error, "error");
            } else {
              showAlert(data.message, "success");
              loadAllUsers(); // Reload the user list
            }
          } catch (error) {
            console.error("Error updating user status:", error);
            showAlert("Failed to update user status", "error");
          }
        };

        window.handleYouTubeAuth = async function (email) {
          try {
            const response = await fetch("/add-user", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                email,
                platform: "youtube",
              }),
            });

            const data = await response.json();
            if (data.authUrl) {
              window.open(data.authUrl, "_blank");
            }
          } catch (error) {
            console.error("Error initiating YouTube auth:", error);
            showAlert("Failed to initiate YouTube authentication", "error");
          }
        };

        window.handleFacebookAuth = async function (email) {
          try {
            const response = await fetch("/add-user", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                email,
                platform: "facebook",
              }),
            });

            const data = await response.json();
            if (data.authUrl) {
              window.open(data.authUrl, "_blank");
            }
          } catch (error) {
            console.error("Error initiating Facebook auth:", error);
            showAlert("Failed to initiate Facebook authentication", "error");
          }
        };

        window.handleInstagramAuth = async function (email) {
          try {
            const response = await fetch("/add-user", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                email,
                platform: "instagram",
              }),
            });

            const data = await response.json();
            if (data.authUrl) {
              window.open(data.authUrl, "_blank");
            }
          } catch (error) {
            console.error("Error initiating Instagram auth:", error);
            showAlert("Failed to initiate Instagram authentication", "error");
          }
        };

        window.viewUserVideos = function (email) {
          fetch(`/user-videos?email=${encodeURIComponent(email)}`)
            .then((response) => response.json())
            .then((data) => {
              const videosContainer = document.getElementById(
                "userVideosContainer"
              );
              const videosList = document.getElementById("videosList");

              videosContainer.style.display = "block";
              videosList.innerHTML = "";

              if (!data.videos || data.videos.length === 0) {
                videosList.innerHTML = "<p>No videos found for this user.</p>";
                return;
              }

              data.videos.forEach((video) => {
                const videoCard = document.createElement("div");
                videoCard.className = "video-card";

                const tagsHtml = video.tags
                  .map((tag) => `<span class="video-tag">${tag}</span>`)
                  .join("");

                const uploadDate = new Date(
                  video.uploadDate
                ).toLocaleDateString();

                videoCard.innerHTML = `
                  <div class="video-title">${video.title}</div>
                  <div class="video-url">
                    <a href="${video.url}" target="_blank">${video.url}</a>
                  </div>
                  <div class="video-description">${video.description.substring(
                    0,
                    200
                  )}${video.description.length > 200 ? "..." : ""}</div>
                  <div class="video-tags">${tagsHtml}</div>
                  <div class="video-date">Uploaded on: ${uploadDate}</div>
                `;

                videosList.appendChild(videoCard);
              });

              showAlert(
                `Found ${data.videos.length} videos for ${email}`,
                "success"
              );
            })
            .catch((error) => {
              console.error("Error fetching videos:", error);
              showAlert("Failed to fetch videos", "error");
            });
        };

        const emailInput = document.getElementById("email");
        const youtubeAuthBtn = document.getElementById("youtubeAuthBtn");
        const facebookAuthBtn = document.getElementById("facebookAuthBtn");
        const instagramAuthBtn = document.getElementById("instagramAuthBtn");

        // Load all users automatically when page loads
        loadAllUsers();

        // Check auth status when email changes
        if (emailInput) {
          emailInput.addEventListener("input", (e) => {
            checkAuthStatus(e.target.value);
          });

          // Add click handlers for auth buttons
          if (youtubeAuthBtn) {
            youtubeAuthBtn.addEventListener("click", () =>
              handleYouTubeAuth(emailInput.value)
            );
          }
          if (facebookAuthBtn) {
            facebookAuthBtn.addEventListener("click", () =>
              handleFacebookAuth(emailInput.value)
            );
          }
          if (instagramAuthBtn) {
            instagramAuthBtn.addEventListener("click", () =>
              handleInstagramAuth(emailInput.value)
            );
          }

          // Initial status check if email is pre-filled
          if (emailInput.value) {
            checkAuthStatus(emailInput.value);
          }
        }

        // Toggle platform credentials visibility
        document.querySelectorAll(".platform-toggle").forEach((toggle) => {
          toggle.addEventListener("change", function () {
            const platform = this.getAttribute("data-platform");
            const credentialsDiv = document.getElementById(
              `${platform}Credentials`
            );
            credentialsDiv.style.display = this.checked ? "block" : "none";
          });
        });

        // Load existing API keys
        fetch("/update-openai-keys", { method: "GET" })
          .then((response) => response.json())
          .then((data) => {
            if (data.keys) {
              apiKeys = data.keys;
              renderKeysList();
            }
          })
          .catch((error) => console.error("Error loading API keys:", error));

        // Add API Key
        document
          .getElementById("addKeyBtn")
          .addEventListener("click", function () {
            const apiKeyInput = document.getElementById("apiKey");
            const key = apiKeyInput.value.trim();

            if (key && !apiKeys.includes(key)) {
              apiKeys.push(key);
              apiKeyInput.value = "";
              renderKeysList();
            } else if (apiKeys.includes(key)) {
              showAlert("This API key already exists in the list.", "error");
            } else {
              showAlert("Please enter a valid API key.", "error");
            }
          });

        // Render API Keys List
        function renderKeysList() {
          const keysList = document.getElementById("keysList");
          keysList.innerHTML = "";

          apiKeys.forEach((key, index) => {
            const li = document.createElement("li");
            li.innerHTML = `
              <span>${maskAPIKey(key)}</span>
              <button type="button" class="remove-key" data-index="${index}">Remove</button>
            `;
            keysList.appendChild(li);
          });

          // Add event listeners to remove buttons
          document.querySelectorAll(".remove-key").forEach((button) => {
            button.addEventListener("click", function () {
              const index = parseInt(this.getAttribute("data-index"));
              apiKeys.splice(index, 1);
              renderKeysList();
            });
          });
        }

        // Mask API Key (show only first 4 and last 4 characters)
        function maskAPIKey(key) {
          if (key.length <= 8) return key;
          return key.substring(0, 4) + "..." + key.substring(key.length - 4);
        }

        // Show Alert
        function showAlert(message, type) {
          const alertContainer = document.getElementById("alert-container");
          const alert = document.createElement("div");
          alert.className = `alert alert-${type}`;
          alert.textContent = message;

          alertContainer.innerHTML = "";
          alertContainer.appendChild(alert);

          // Auto-hide after 5 seconds
          setTimeout(() => {
            alert.style.display = "none";
          }, 5000);
        }

        // Submit API Keys Form
        document
          .getElementById("apiKeysForm")
          .addEventListener("submit", function (e) {
            e.preventDefault();

            fetch("/update-openai-keys", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({ keys: apiKeys }),
            })
              .then((response) => response.json())
              .then((data) => {
                showAlert(
                  data.message || "API keys saved successfully.",
                  "success"
                );
              })
              .catch((error) => {
                console.error("Error saving API keys:", error);
                showAlert("Failed to save API keys.", "error");
              });
          });

        // Add this at the beginning of your script section
        async function loadSavedConfigurations() {
          try {
            console.log("Fetching saved configurations...");
            const response = await fetch("/get-saved-configs");
            console.log("Response status:", response.status);

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log("Received data:", data);

            const dropdown = document.getElementById("savedConfigs");
            if (!dropdown) {
              console.error("Dropdown element not found!");
              return;
            }

            // Clear existing options except the first one
            while (dropdown.options.length > 1) {
              dropdown.remove(1);
            }

            // Add saved configurations to dropdown
            if (data.configs) {
              console.log("Processing configurations:", data.configs);
              const configEntries = Object.entries(data.configs);
              console.log("Number of configurations:", configEntries.length);

              configEntries.forEach(([configName, configData]) => {
                console.log(
                  "Adding config to dropdown:",
                  configName,
                  configData
                );
                const option = document.createElement("option");
                option.value = configName;
                option.textContent = `${configName} (${
                  configData.email || "No email"
                })`;
                dropdown.appendChild(option);
              });
            } else {
              console.log("No configurations found in response");
            }
          } catch (error) {
            console.error("Error loading configurations:", error);
            showAlert("Failed to load saved configurations", "error");
          }
        }

        // Make sure configurations are loaded when the page loads
        window.addEventListener("load", function () {
          console.log("Window loaded, loading configurations...");
          loadSavedConfigurations();
        });

        // Also load when DOM is ready
        document.addEventListener("DOMContentLoaded", function () {
          console.log("DOM loaded, loading configurations...");
          loadSavedConfigurations();
        });

        // Handle configuration selection
        document
          .getElementById("savedConfigs")
          .addEventListener("change", async function (e) {
            if (e.target.value) {
              console.log("Selected configuration:", e.target.value);
              try {
                const response = await fetch(
                  `/get-config/${encodeURIComponent(e.target.value)}`
                );
                if (!response.ok) {
                  throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                console.log("Loaded configuration data:", data);

                if (data.config) {
                  document.getElementById("email").value =
                    data.config.email || "";
                  document.getElementById("folderId").value =
                    data.config.folderId || "";
                  document.getElementById("folderDescription").value =
                    data.config.folderDescription || "";
                  document.getElementById("videoType").value =
                    data.config.videoType || "";
                  document.getElementById("cronSchedule").value =
                    data.config.cronSchedule || "";
                  document.getElementById("configName").value = e.target.value;
                }
              } catch (error) {
                console.error("Error loading configuration:", error);
                showAlert("Failed to load configuration", "error");
              }
            }
          });

        // Handle save as new configuration
        document
          .getElementById("saveAsNewBtn")
          .addEventListener("click", async function () {
            const configName = document.getElementById("configName").value;
            if (!configName) {
              showAlert("Please enter a configuration name", "error");
              return;
            }

            console.log("Saving new configuration:", configName);
            const formData = {
              email: document.getElementById("email").value,
              folderId: document.getElementById("folderId").value,
              folderDescription:
                document.getElementById("folderDescription").value,
              videoType: document.getElementById("videoType").value,
              cronSchedule: document.getElementById("cronSchedule").value,
            };
            console.log("Configuration data:", formData);

            try {
              const response = await fetch("/save-config", {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({
                  name: configName,
                  config: formData,
                }),
              });

              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
              }

              const data = await response.json();
              console.log("Save response:", data);

              if (data.error) {
                throw new Error(data.error);
              }

              await loadSavedConfigurations();
              showAlert("Configuration saved successfully", "success");
            } catch (error) {
              console.error("Error saving configuration:", error);
              showAlert(
                "Failed to save configuration: " + error.message,
                "error"
              );
            }
          });

        // Handle delete configuration
        document
          .getElementById("deleteConfigBtn")
          .addEventListener("click", async function () {
            const configName = document.getElementById("savedConfigs").value;
            if (!configName) {
              showAlert("Please select a configuration to delete", "error");
              return;
            }

            console.log("Deleting configuration:", configName);
            try {
              const response = await fetch(
                `/delete-config/${encodeURIComponent(configName)}`,
                {
                  method: "DELETE",
                }
              );

              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
              }

              const data = await response.json();
              console.log("Delete response:", data);

              if (data.error) {
                throw new Error(data.error);
              }

              await loadSavedConfigurations();
              showAlert("Configuration deleted successfully", "success");
            } catch (error) {
              console.error("Error deleting configuration:", error);
              showAlert(
                "Failed to delete configuration: " + error.message,
                "error"
              );
            }
          });
      });
    </script>
  </body>
</html>
