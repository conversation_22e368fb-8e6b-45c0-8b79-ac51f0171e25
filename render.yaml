services:
  - type: web
    name: automation-app
    env: docker
    plan: free
    dockerfilePath: ./Dockerfile
    envVars:
      - key: FIREBASE_API_KEY
        value: "AIzaSyCK3rtCNVe2bTGXFejL48fI7Xhc2LCNIzk"
      - key: FIREBASE_AUTH_DOMAIN
        value: "uploader-f483c.firebaseapp.com"
      - key: FIREBASE_PROJECT_ID
        value: "uploader-f483c"
      - key: FIREBASE_STORAGE_BUCKET
        value: "uploader-f483c.appspot.com"
      - key: FIREBASE_MESSAGING_SENDER_ID
        value: "65681872939"
      - key: FIREBASE_APP_ID
        value: "dfc304a8d84663f01d326e"
      - key: FIREBASE_PRIVATE_KEY
        sync: false
      - key: FIREBASE_CLIENT_EMAIL
        value: "<EMAIL>"
      - key: META_APP_ID
        value: "1458506455111426"
      - key: META_APP_SECRET
        value: "c24f47b5f038d11ed950bdde584786d5"
      - key: InstagramappID
        value: "8999128336859700"
      - key: Instagram_app_secret
        value: "595d79857ed1b043bd217ce1bb1098e8"
      - key: GOOGLE_DRIVE_FOLDER_ID
        value: "19inwPy2_ihm5IOkGfanqmljceXg320QI"
      - key: PORT
        value: "3000"
    healthCheckPath: /health
    autoDeploy: false 