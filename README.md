# YouTube Automation with Google Drive Integration

This project provides an automated solution for managing YouTube content with Google Drive integration. It's built with Node.js, TypeScript, and Express, offering features for video management and automation.

## Features

- YouTube video management and automation
- Google Drive integration
- Firebase integration for data storage
- Instagram API integration
- Automated scheduling with cron jobs
- RESTful API endpoints
- TypeScript support

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- Google Cloud Platform account with YouTube Data API enabled
- Firebase account (for authentication and storage)
- Instagram account (for cross-posting features)

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd automation
```

2. Install dependencies:

```bash
npm install
```

3. Configure environment variables:
   Create a `.env` file in the root directory and add the following variables:

```env
# Add your environment variables here
# See .env.example for required variables
```

4. Set up Google Cloud credentials:

- Create a project in Google Cloud Console
- Enable YouTube Data API
- Download your OAuth2 credentials and save them as `oauth2-user.json` in the `src/utils` directory

## Development

To run the project in development mode:

```bash
npm run dev
```

This will start the server using ts-node with hot-reloading enabled.

## Building for Production

1. Build the project:

```bash
npm run build
```

2. Start the production server:

```bash
npm start
```

## Project Structure

```
├── server.ts          # Main server file
├── src/
│   ├── public/       # Static files
│   └── utils/        # Utility functions and configurations
├── build/            # Compiled JavaScript files
└── tsconfig.json     # TypeScript configuration
```

## Docker Support

The project includes Docker support for containerized deployment:

```bash
# Build the Docker image
docker build -t youtube-automation .

# Run with docker-compose
docker-compose up
```

## Scripts

- `npm run dev` - Start development server
- `npm run build` - Build the project
- `npm start` - Start production server
- `npm run copy-public` - Copy public assets to build directory

## License

ISC

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request
