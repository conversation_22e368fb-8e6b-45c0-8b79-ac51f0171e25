{"name": "automation", "version": "1.0.0", "description": "YouTube automation with Google Drive integration", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "copy-public": "cp -r ./src/public build/public", "build": "tsc && cp -r src/public build/src/", "postbuild": "mkdir -p build/public && cp -r src/public build/", "start": "node build/server.js", "dev": "nodemon --watch '*.ts' --exec 'ts-node' server.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@google-cloud/storage": "^7.15.2", "axios": "^1.8.4", "body-parser": "^1.20.3", "cron": "^4.1.3", "dotenv": "^16.4.7", "express": "^4.21.2", "firebase": "^10.14.1", "firebase-admin": "^12.7.0", "form-data": "^4.0.2", "fs": "^0.0.1-security", "google-auth-library": "^9.15.1", "googleapis": "^146.0.0", "instagram-private-api": "^1.46.1", "node-cron": "^3.0.3", "nodemon": "^3.1.9", "open": "^10.1.0", "path": "^0.12.7", "readline": "^1.3.0"}, "devDependencies": {"@types/express": "^5.0.0", "@types/node-cron": "^3.0.11", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "repository": {"type": "git"}, "bugs": {}}