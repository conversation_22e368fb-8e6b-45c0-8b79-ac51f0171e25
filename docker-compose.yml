version: '3.8'

services:
  docker-test:
    build: .
    container_name: docker-test
    restart: always
    ports:
      - "3000:3000"
    environment:
      # Firebase Configuration
      - FIREBASE_API_KEY=${FIREBASE_API_KEY}
      - FIREBASE_AUTH_DOMAIN=${FIREBASE_AUTH_DOMAIN}
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_STORAGE_BUCKET=${FIREBASE_STORAGE_BUCKET}
      - FIREBASE_MESSAGING_SENDER_ID=${FIREBASE_MESSAGING_SENDER_ID}
      - FIREBASE_APP_ID=${FIREBASE_APP_ID}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}

      # Other configurations
      - NODE_ENV=${NODE_ENV}
      - PORT=${PORT}
      - GOOGLE_DRIVE_FOLDER_ID=${GOOGLE_DRIVE_FOLDER_ID}

      # Meta/Facebook Configuration
      - META_APP_ID=${META_APP_ID}
      - META_APP_SECRET=${META_APP_SECRET}

      # Instagram Configuration
      - InstagramappID=${InstagramappID}
      - Instagram_app_secret=${Instagram_app_secret}
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
      - /usr/src/app/build
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:3000/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
