#!/bin/bash

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        echo "Docker is not running. Starting Docker..."
        sudo service docker start
        sleep 10
    fi
}

# Function to start the application
start_app() {
    echo "Starting YouTube Automation..."
    docker-compose up -d --build
}

# Function to check container health
check_health() {
    container_name="docker-test"
    if [ "$(docker inspect -f '{{.State.Health.Status}}' $container_name 2>/dev/null)" == "unhealthy" ]; then
        echo "Container is unhealthy. Restarting..."
        docker-compose restart
    fi
}

# Main execution
check_docker
start_app

# Set up continuous health monitoring
while true; do
    check_health
    sleep 30
done 