import express, { Request, Response } from "express";
import * as fs from "fs";
import * as path from "path";
import bodyParser from "body-parser";
import {
  authenticateUser,
  getNewToken,
  scheduleUploadsForUser,
  uploadToSocialPlatforms,
} from "./src/utils/scheduler";

import {
  initializeFirebaseAdmin,
  getFirestore,
  storeOAuth2Credentials,
  getOAuth2Credentials,
} from "./src/utils/firebase";
import dotenv from "dotenv";

dotenv.config();

// Initialize Firebase Admin
initializeFirebaseAdmin();

const app = express();
const PORT = 3000;
const redirectUri =
  "https://automation-app-1pxz.onrender.com/facebook-callback";
app.use(bodyParser.urlencoded({ extended: true }));
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, "src/public")));

// Ensure temp directory exists for temporary files
const tempDir = path.join(__dirname, "src/utils/temp");
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

// Store pending authentication requests
const pendingAuthRequests: { [key: string]: string } = {};

app.get("/", (req: Request, res: Response) => {
  res.sendFile(path.join(__dirname, "src/public/index.html"));
});

// Initialize Application - Store OAuth2 credentials in Firebase if they don't exist yet
app.get("/initialize", async (req: Request, res: any) => {
  try {
    // Check if OAuth2 credentials already exist
    try {
      await getOAuth2Credentials();
      return res
        .status(200)
        .json({ message: "Application already initialized" });
    } catch (error) {
      // OAuth2 credentials don't exist, we need to create them
      console.log(
        "OAuth2 credentials not found, creating from environment variables"
      );
    }

    // Create from environment variables
    const credentials = {
      installed: {
        client_id: process.env.OAUTH2_CLIENT_ID,
        project_id: process.env.OAUTH2_PROJECT_ID,
        auth_uri: "https://accounts.google.com/o/oauth2/auth",
        token_uri: "https://oauth2.googleapis.com/token",
        auth_provider_x509_cert_url:
          "https://www.googleapis.com/oauth2/v1/certs",
        client_secret: process.env.OAUTH2_CLIENT_SECRET,
        redirect_uris: [
          "https://automation-app-1pxz.onrender.com/oauth-callback",
        ],
      },
    };

    // Store in Firebase
    await storeOAuth2Credentials(credentials);

    res.status(200).json({ message: "Application initialized successfully" });
  } catch (error) {
    console.error("Error initializing application:", error);
    res.status(500).json({ error: "Failed to initialize application" });
  }
});

// Load users from Firebase
const loadUsers = async (): Promise<any[]> => {
  try {
    const db = getFirestore();
    const usersSnapshot = await db.collection("users").get();

    if (!usersSnapshot.empty) {
      return usersSnapshot.docs.map((doc) => doc.data());
    }

    return [];
  } catch (error) {
    console.error("Error reading users from Firebase:", error);
    return [];
  }
};

// Save users to Firebase
const saveUsers = async (users: any[]): Promise<void> => {
  try {
    const db = getFirestore();
    const batch = db.batch();

    // Clear existing users
    const usersSnapshot = await db.collection("users").get();
    usersSnapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    // Add new users
    users.forEach((user, index) => {
      const userRef = db.collection("users").doc(user.email || `user_${index}`);
      batch.set(userRef, user);
    });

    await batch.commit();
  } catch (error) {
    console.error("Error saving users to Firebase:", error);
    throw error;
  }
};

// OAuth callback route for YouTube
app.get("/oauth-callback", async (req: Request, res: any) => {
  const { code, state } = req.query;
  if (!code || !state) {
    return res.status(400).send("Missing code or state parameter");
  }

  try {
    const email = pendingAuthRequests[state as string];
    if (!email) {
      return res.status(400).send("Invalid state parameter");
    }

    let users = await loadUsers();
    let user = users.find((u) => u.email === email);

    if (!user) {
      return res.status(400).send("User not found");
    }

    // Get the new token and save to Firebase
    const auth = await getNewToken(code as string, user.email);

    // Update user's platform status
    user.platformStatus.youtube = true;
    await saveUsers(users);

    // Start scheduling uploads for this user immediately
    try {
      await scheduleUploadsForUser(user);
      console.log(`Scheduled uploads for ${user.email}`);
    } catch (scheduleError) {
      console.error(
        `Error scheduling uploads for ${user.email}:`,
        scheduleError
      );
    }

    // Clean up the pending request
    delete pendingAuthRequests[state as string];

    // Success response HTML
    res.send(`
      <html>
        <head>
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 50px;
            }
            .success {
              color: #28a745;
              font-size: 24px;
              margin-bottom: 20px;
            }
            .message {
              margin-bottom: 20px;
            }
          </style>
        </head>
        <body>
          <div class="success">✅ YouTube Authentication successful!</div>
          <div class="message">Your YouTube account has been connected successfully.</div>
          <div class="message">You can close this window and videos will be uploaded to your YouTube channel.</div>
        </body>
      </html>
    `);
  } catch (error) {
    console.error("Error in OAuth callback:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Facebook callback route
app.get("/facebook-callback", async (req: Request, res: any) => {
  const { code, state } = req.query;
  if (!code || !state) {
    return res.status(400).send("Missing code or state parameter");
  }

  try {
    const email = pendingAuthRequests[state as string];
    if (!email) {
      return res.status(400).send("Invalid state parameter");
    }

    let users = await loadUsers();
    let user = users.find((u) => u.email === email);

    if (!user) {
      return res.status(400).send("User not found");
    }

    const appId = process.env.META_APP_ID;
    const appSecret = process.env.META_APP_SECRET;

    // Exchange code for access token
    const response = await fetch(
      `https://graph.facebook.com/v18.0/oauth/access_token?client_id=${appId}&redirect_uri=${encodeURIComponent(
        redirectUri
      )}&client_secret=${appSecret}&code=${code}`
    );

    const data = await response.json();
    if (data.error) {
      throw new Error(data.error.message);
    }

    const accessToken = data.access_token;
    const expiresIn = data.expires_in;

    // Get user's Facebook pages
    console.log("Fetching Facebook pages...");
    const pagesResponse = await fetch(
      `https://graph.facebook.com/v18.0/me/accounts?access_token=${accessToken}&fields=id,name,access_token,category`
    );

    const pagesData = await pagesResponse.json();
    console.log("Facebook pages response:", JSON.stringify(pagesData, null, 2));

    if (pagesData.error) {
      console.error("Error fetching Facebook pages:", pagesData.error);
      throw new Error(
        `Failed to fetch Facebook pages: ${pagesData.error.message}`
      );
    }

    if (!pagesData.data || pagesData.data.length === 0) {
      console.warn("No Facebook pages found for the user");
    } else {
      console.log(
        `Found ${pagesData.data.length} Facebook pages:`,
        pagesData.data.map((page) => page.name)
      );
    }

    // Store tokens in Firebase
    const db = getFirestore();
    const tokenDoc = await db.collection("userTokens").doc(email).get();
    const existingTokens = tokenDoc.exists ? tokenDoc.data() : {};

    // Only update Facebook token if it's different
    if (
      !existingTokens.facebook ||
      existingTokens.facebook.accessToken !== accessToken
    ) {
      await db
        .collection("userTokens")
        .doc(email)
        .set({
          ...existingTokens,
          facebook: {
            accessToken,
            expiresIn,
            expiresAt: new Date(Date.now() + expiresIn * 1000).toISOString(),
            pages: pagesData.data || [],
          },
          updatedAt: new Date().toISOString(),
        });
    }

    // Update user's platform status
    user.platformStatus.facebook = true;
    await saveUsers(users);

    // Clean up the pending request
    delete pendingAuthRequests[state as string];

    // Success response HTML
    res.send(`
      <html>
        <head>
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 50px;
            }
            .success {
              color: #28a745;
              font-size: 24px;
              margin-bottom: 20px;
            }
            .message {
              margin-bottom: 20px;
            }
          </style>
        </head>
        <body>
          <div class="success">✅ Facebook Authentication successful!</div>
          <div class="message">Your Facebook account has been connected successfully.</div>
          <div class="message">You can close this window and videos will be uploaded to your Facebook pages.</div>
        </body>
      </html>
    `);
  } catch (error) {
    console.error("Error in Facebook callback:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Instagram callback route
app.get("/instagram-callback", async (req: Request, res: any) => {
  console.log("instagram-callback", req.query);
  const { code, state } = req.query;
  if (!code || !state) {
    return res.status(400).send("Missing code or state parameter");
  }

  try {
    const email = pendingAuthRequests[state as string];
    if (!email) {
      return res.status(400).send("Invalid state parameter");
    }

    let users = await loadUsers();
    let user = users.find((u) => u.email === email);

    if (!user) {
      return res.status(400).send("User not found");
    }

    const appId = process.env.InstagramappID;
    const appSecret = process.env.Instagram_app_secret;
    const redirectUri =
      "https://automation-app-1pxz.onrender.com/instagram-callback";

    // Exchange code for access token
    const response = await fetch(
      `https://api.instagram.com/oauth/access_token`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: new URLSearchParams({
          client_id: appId,
          client_secret: appSecret,
          grant_type: "authorization_code",
          redirect_uri: redirectUri,
          code: code as string,
        }),
      }
    );

    const data = await response.json();
    if (data.error) {
      throw new Error(data.error.message);
    }

    const accessToken = data.access_token;
    const userId = data.user_id;

    // Get long-lived access token
    const longLivedResponse = await fetch(
      `https://graph.instagram.com/access_token?grant_type=ig_exchange_token&client_secret=${appSecret}&access_token=${accessToken}`
    );

    const longLivedData = await longLivedResponse.json();
    if (longLivedData.error) {
      throw new Error(longLivedData.error.message);
    }

    // Store tokens in Firebase
    const db = getFirestore();
    const tokenDoc = await db.collection("userTokens").doc(email).get();
    const existingTokens = tokenDoc.exists ? tokenDoc.data() : {};

    // Only update Instagram token if it's different
    if (
      !existingTokens.instagram ||
      existingTokens.instagram.accessToken !== longLivedData.access_token
    ) {
      await db
        .collection("userTokens")
        .doc(email)
        .set({
          ...existingTokens,
          instagram: {
            accessToken: longLivedData.access_token,
            expiresIn: longLivedData.expires_in,
            expiresAt: new Date(
              Date.now() + longLivedData.expires_in * 1000
            ).toISOString(),
            userId,
          },
          updatedAt: new Date().toISOString(),
        });
    }

    // Update user's platform status
    user.platformStatus.instagram = true;
    await saveUsers(users);

    // Clean up the pending request
    delete pendingAuthRequests[state as string];

    // Success response HTML
    res.send(`
      <html>
        <head>
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              padding: 50px;
            }
            .success {
              color: #28a745;
              font-size: 24px;
              margin-bottom: 20px;
            }
            .message {
              margin-bottom: 20px;
            }
          </style>
        </head>
        <body>
          <div class="success">✅ Instagram Authentication successful!</div>
          <div class="message">Your Instagram account has been connected successfully.</div>
          <div class="message">You can close this window and videos will be uploaded to your Instagram account.</div>
        </body>
      </html>
    `);
  } catch (error) {
    console.error("Error in Instagram callback:", error);
    res.status(500).send("Internal Server Error");
  }
});

// Get user tokens route
app.get("/user-tokens", async (req: Request, res: any) => {
  try {
    const email = req.query.email as string;
    if (!email) {
      return res.status(400).json({ error: "Email is required" });
    }

    const db = getFirestore();
    const tokenDoc = await db.collection("userTokens").doc(email).get();

    if (!tokenDoc.exists) {
      return res.json({});
    }

    const data = tokenDoc.data();
    res.json(data);
  } catch (error) {
    console.error("Error getting user tokens:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// Add user route - modified to handle platform-specific authentication
app.post("/add-user", async (req: Request, res: any) => {
  try {
    const { email, platform, folderId, videoType, cronSchedule } = req.body;

    if (!email) {
      return res.status(400).json({ error: "Email is required" });
    }

    let users = await loadUsers();
    let user = users.find((u) => u.email === email);

    if (!user) {
      user = {
        email,
        googleDriveLinks: [],
        isActive: true,
        platformStatus: {
          youtube: false,
          facebook: false,
          instagram: false,
        },
      };
      users.push(user);
    }

    // If folder details are provided, add or update the folder
    if (folderId && videoType && cronSchedule) {
      const existingFolderIndex = user.googleDriveLinks.findIndex(
        (link: any) => link.folderId === folderId
      );

      if (existingFolderIndex === -1) {
        user.googleDriveLinks.push({
          folderId,
          videoType,
          cronSchedule,
        });
      } else {
        user.googleDriveLinks[existingFolderIndex] = {
          folderId,
          videoType,
          cronSchedule,
        };
      }
    }

    // Save updated user data
    await saveUsers(users);

    // If platform is specified, handle authentication
    if (platform) {
      // Generate a unique state parameter to identify this auth request
      const stateParam = Math.random().toString(36).substring(2, 15);
      // Store the email with the state parameter
      pendingAuthRequests[stateParam] = email;

      if (platform === "youtube") {
        const auth = await authenticateUser(user);
        if (auth) {
          const SCOPES = [
            "https://www.googleapis.com/auth/drive",
            "https://www.googleapis.com/auth/drive.file",
            "https://www.googleapis.com/auth/drive.metadata.readonly",
            "https://www.googleapis.com/auth/drive.readonly",
            "https://www.googleapis.com/auth/youtube.upload",
            "https://www.googleapis.com/auth/youtube.readonly",
          ];

          const authUrl = auth.generateAuthUrl({
            access_type: "offline",
            scope: SCOPES,
            prompt: "consent",
            state: stateParam,
          });

          return res.status(200).json({
            message:
              "Authentication required. Please authorize in the browser window that opened.",
            authUrl,
          });
        }
      } else if (platform === "facebook") {
        const appId = process.env.META_APP_ID;
        const authUrl = `https://www.facebook.com/v18.0/dialog/oauth?client_id=${appId}&redirect_uri=${encodeURIComponent(
          redirectUri
        )}&scope=pages_manage_posts,pages_read_engagement,pages_show_list,public_profile,pages_manage_metadata,instagram_basic,instagram_content_publish&state=${stateParam}`;

        return res.status(200).json({
          message:
            "Authentication required. Please authorize in the browser window that opened.",
          authUrl,
        });
      } else if (platform === "instagram") {
        const authUrl = `https://www.instagram.com/oauth/authorize?enable_fb_login=0&force_authentication=1&client_id=8999128336859700&redirect_uri=https://automation-app-1pxz.onrender.com/instagram-callback&response_type=code&scope=instagram_business_basic%2Cinstagram_business_content_publish%2Cinstagram_business_manage_insights&state=${stateParam}`;

        return res.status(200).json({
          message:
            "Authentication required. Please authorize in the browser window that opened.",
          authUrl,
        });
      }
    }

    return res.status(200).json({
      message: "User configuration saved successfully.",
    });
  } catch (error) {
    console.error("Error adding user:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
});

// Schedule uploads route
app.post("/schedule-uploads", async (req: Request, res: any) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({ error: "Email is required" });
  }

  try {
    let users = await loadUsers();
    let user = users.find((u) => u.email === email);

    if (!user) {
      return res.status(400).json({ error: "User not found" });
    }

    // Check if user has any platform tokens
    const db = getFirestore();
    const tokenDoc = await db.collection("userTokens").doc(email).get();

    if (!tokenDoc.exists) {
      return res.status(400).json({
        error:
          "User not authenticated with any platform. Please authenticate first.",
      });
    }

    const tokens = tokenDoc.data();
    if (!tokens.youtube && !tokens.facebook && !tokens.instagram) {
      return res.status(400).json({
        error:
          "User not authenticated with any platform. Please authenticate first.",
      });
    }

    await scheduleUploadsForUser(user);
    res.status(200).json({ message: "Uploads scheduled successfully" });
  } catch (error) {
    console.error("Error scheduling uploads:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// Route to trigger immediate upload to social platforms
app.post("/upload-now", async (req: Request, res: any) => {
  const { email, folderId } = req.body;

  if (!email || !folderId) {
    return res.status(400).json({ error: "Email and folder ID are required" });
  }

  try {
    let users = await loadUsers();
    let user = users.find((u) => u.email === email);

    if (!user) {
      return res.status(400).json({ error: "User not found" });
    }

    // Check if user has any platform tokens
    const db = getFirestore();
    const tokenDoc = await db.collection("userTokens").doc(email).get();

    if (!tokenDoc.exists) {
      return res.status(400).json({
        error:
          "User not authenticated with any platform. Please authenticate first.",
      });
    }

    const tokens = tokenDoc.data();
    if (!tokens.youtube && !tokens.facebook && !tokens.instagram) {
      return res.status(400).json({
        error:
          "User not authenticated with any platform. Please authenticate first.",
      });
    }

    // Check if the folder ID exists for this user
    const folderData = user.googleDriveLinks.find(
      (link: any) => link.folderId === folderId
    );
    if (!folderData) {
      return res
        .status(400)
        .json({ error: "Folder ID not found for this user" });
    }

    const auth = await authenticateUser(user);
    console.log("auth22", auth);
    // Trigger immediate upload - we'll load OpenAI keys in the function
    await uploadToSocialPlatforms(auth, user, folderData, tokens);

    res.status(200).json({
      message:
        "Upload triggered for all configured platforms. Check console for progress.",
    });
  } catch (error) {
    console.error("Error triggering upload:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// Get user's uploaded videos
app.get("/user-videos", async (req: Request, res: any) => {
  const { email } = req.query;

  if (!email) {
    return res.status(400).json({ error: "Email parameter is required" });
  }
  console.log("email", email);
  try {
    const db = getFirestore();
    const userVideoRef = db.collection("userVideos").doc(email as string);
    const userVideosCollection = userVideoRef.collection("videos");
    const videosSnapshot = await userVideosCollection.get();
    console.log(email, "videosSnapshot", videosSnapshot);

    if (videosSnapshot.empty) {
      return res.status(200).json({ videos: [] });
    }

    const videos = videosSnapshot.docs.map((doc) => doc.data());

    return res.status(200).json({
      videos,
      count: videos.length,
    });
  } catch (error) {
    console.error("Error fetching user videos:", error);
    return res.status(500).json({ error: "Failed to fetch videos" });
  }
});

// Get all users route
app.get("/users", async (req: Request, res: any) => {
  try {
    const users = await loadUsers();
    res.status(200).json({ users });
  } catch (error) {
    console.error("Error fetching users:", error);
    res.status(500).json({ error: "Failed to fetch users" });
  }
});

// Update platform status endpoint
app.post("/update-platform-status", async (req: Request, res: any) => {
  try {
    const { email, platform, isActive } = req.body;

    if (!email || !platform) {
      return res.status(400).json({ error: "Email and platform are required" });
    }

    // Validate platform
    if (!["youtube", "instagram", "facebook"].includes(platform)) {
      return res.status(400).json({
        error: "Invalid platform. Must be youtube, instagram, or facebook",
      });
    }

    const users = await loadUsers();
    const userIndex = users.findIndex((u) => u.email === email);

    if (userIndex === -1) {
      return res.status(404).json({ error: "User not found" });
    }

    const user = users[userIndex];

    // Initialize platform settings if they don't exist
    if (!user.platformStatus) {
      user.platformStatus = { youtube: true, instagram: true, facebook: true };
    }

    // Update specific platform status
    user.platformStatus[platform] = isActive;

    // Save updated users
    await saveUsers(users);

    // If any platform is active, make sure user is active and schedule uploads
    const anyPlatformActive = Object.values(user.platformStatus).some(
      (status) => status === true
    );

    if (anyPlatformActive && !user.isActive) {
      // Activate user if any platform is active
      user.isActive = true;
      await saveUsers(users);

      try {
        await scheduleUploadsForUser(user);
        console.log(
          `Rescheduled uploads for ${email} after platform activation`
        );
      } catch (err) {
        console.error(`Failed to reschedule uploads for ${email}:`, err);
      }
    }

    res.status(200).json({
      message: `${
        platform.charAt(0).toUpperCase() + platform.slice(1)
      } for ${email} ${isActive ? "activated" : "deactivated"} successfully`,
      user: users[userIndex],
    });
  } catch (error) {
    console.error("Error updating platform status:", error);
    res.status(500).json({ error: "Failed to update platform status" });
  }
});

// Update user status endpoint
app.post("/update-user-status", async (req: Request, res: any) => {
  try {
    const { email, isActive } = req.body;

    if (!email) {
      return res.status(400).json({ error: "Email is required" });
    }

    const users = await loadUsers();
    const userIndex = users.findIndex((u) => u.email === email);

    if (userIndex === -1) {
      return res.status(404).json({ error: "User not found" });
    }

    const user = users[userIndex];
    user.isActive = isActive;

    // If user is being activated, initialize platform status
    if (isActive && !user.platformStatus) {
      user.platformStatus = {
        youtube: false,
        instagram: false,
        facebook: false,
      };
    }

    // Save updated users
    await saveUsers(users);

    // If user is being activated, schedule uploads
    if (isActive) {
      try {
        await scheduleUploadsForUser(user);
        console.log(`Scheduled uploads for ${email} after activation`);
      } catch (err) {
        console.error(`Failed to schedule uploads for ${email}:`, err);
      }
    }

    res.status(200).json({
      message: `User ${email} ${
        isActive ? "activated" : "deactivated"
      } successfully`,
      user: users[userIndex],
    });
  } catch (error) {
    console.error("Error updating user status:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// Add health check endpoint
app.get("/health", (req: Request, res: any) => {
  res.status(200).json({ status: "healthy" });
});

// Add configuration management routes
app.get("/get-saved-configs", async (req: Request, res: any) => {
  try {
    const db = getFirestore();
    console.log("Fetching saved configurations from Firebase...");

    const configsSnapshot = await db.collection("savedConfigurations").get();
    console.log(`Found ${configsSnapshot.size} configurations in Firebase`);

    const configs: { [key: string]: any } = {};

    configsSnapshot.forEach((doc) => {
      // console.log(`Processing configuration document: ${doc.id}`);
      configs[doc.id] = doc.data();
      // console.log(`Configuration data for ${doc.id}:`, doc.data());
    });

    console.log("Number of configurations:", Object.keys(configs).length);

    res.status(200).json({ configs });
  } catch (error) {
    console.error("Error fetching configurations:", error);
    res.status(500).json({
      error: "Failed to fetch configurations",
      details: error.message,
    });
  }
});

app.get("/get-config/:name", async (req: Request, res: any) => {
  try {
    const { name } = req.params;
    const db = getFirestore();
    const configDoc = await db
      .collection("savedConfigurations")
      .doc(name)
      .get();

    if (!configDoc.exists) {
      return res.status(404).json({ error: "Configuration not found" });
    }

    res.status(200).json({ config: configDoc.data() });
  } catch (error) {
    console.error("Error fetching configuration:", error);
    res.status(500).json({ error: "Failed to fetch configuration" });
  }
});

app.post("/save-config", async (req: Request, res: any) => {
  try {
    const { name, config } = req.body;
    if (!name || !config) {
      return res.status(400).json({ error: "Name and config are required" });
    }

    console.log("Saving configuration:", { name, config });
    const db = getFirestore();

    // First, ensure user exists in users collection
    if (config.email) {
      let users = await loadUsers();
      let user = users.find((u) => u.email === config.email);

      if (!user) {
        // Create new user
        user = {
          email: config.email,
          googleDriveLinks: [],
          isActive: true,
          platformStatus: {
            youtube: false,
            facebook: false,
            instagram: false,
          },
        };
        users.push(user);
      }

      // Update or add folder data to user's googleDriveLinks
      if (config.folderId && config.videoType && config.cronSchedule) {
        const existingFolderIndex = user.googleDriveLinks.findIndex(
          (link: any) => link.folderId === config.folderId
        );

        const folderData = {
          folderId: config.folderId,
          videoType: config.videoType,
          cronSchedule: config.cronSchedule,
        };

        if (existingFolderIndex === -1) {
          user.googleDriveLinks.push(folderData);
        } else {
          user.googleDriveLinks[existingFolderIndex] = folderData;
        }

        await saveUsers(users);
        console.log(
          `Updated googleDriveLinks for user: ${config.email}`,
          user.googleDriveLinks
        );
      }
    }

    // Save the configuration
    await db
      .collection("savedConfigurations")
      .doc(name)
      .set({
        ...config,
        createdAt: new Date().toISOString(),
      });

    console.log(`Configuration ${name} saved successfully`);
    res.status(200).json({ message: "Configuration saved successfully" });
  } catch (error) {
    console.error("Error saving configuration:", error);
    res.status(500).json({ error: "Failed to save configuration" });
  }
});

// Add debug route for environment variables
app.get("/debug/env", (req: Request, res: Response) => {
  const safeEnvVars = {
    FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
    FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL,
    FIREBASE_AUTH_DOMAIN: process.env.FIREBASE_AUTH_DOMAIN,
    FIREBASE_STORAGE_BUCKET: process.env.FIREBASE_STORAGE_BUCKET,
    FIREBASE_MESSAGING_SENDER_ID: process.env.FIREBASE_MESSAGING_SENDER_ID,
    FIREBASE_APP_ID: process.env.FIREBASE_APP_ID,
    NODE_ENV: process.env.NODE_ENV,
    PORT: process.env.PORT,
    // Add any other non-sensitive environment variables you want to check
  };

  res.json({
    environment: safeEnvVars,
    privateKeyExists: !!process.env.FIREBASE_PRIVATE_KEY,
    privateKeyLength: process.env.FIREBASE_PRIVATE_KEY?.length || 0,
  });
});

// Start the server
app.listen(PORT, async () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);

  // Check if application is initialized
  try {
    await getOAuth2Credentials();
    console.log("OAuth2 credentials found in Firebase");
  } catch (error) {
    console.warn(
      "⚠️ Application not initialized. Please visit /initialize endpoint"
    );
  }

  // Load existing users and schedule their uploads on server start
  try {
    const users = await loadUsers();
    for (const user of users) {
      if (user?.isActive) {
        // // console.log("user", user);
        // const token = await loadOAuthToken(user.email);
        // if (token) {
        try {
          await scheduleUploadsForUser(user);
          console.log(`Scheduled uploads for ${user.email} on server start`);
        } catch (err) {
          console.error(`Failed to schedule uploads for ${user.email}:`, err);
        }
        // } else {
        //   console.log(`No token found for ${user.email}, skipping scheduling`);
        // }
      }
    }
  } catch (error) {
    console.error("Error loading users on server start:", error);
  }
});
